from faker import Faker
import random
import csv
from datetime import datetime, timedelta
import json
import ipaddress
import uuid

class CSVGenerator:
    def __init__(self):
        self.faker = Faker()

        # 根据README.md定义字段列表
        self.fields = [
            ('request_id', 'String'),
            ('log_time', 'String'),
            ('msec', 'Long'),
            ('request_time', 'Double'),
            ('stream_protocol', 'String'),
            ('duration', 'Integer'),
            ('total_duration', 'Integer'),
            ('client_addr', 'String'),
            ('server_addr', 'String'),
            ('scheme', 'String'),
            ('http_method', 'String'),
            ('domain', 'String'),
            ('request_uri', 'String'),
            ('rewrite_uri', 'String'),
            ('status', 'Integer'),
            ('bytes_sent', 'Integer'),
            ('interval_bytes_sent', 'Long'),
            ('body_bytes_sent', 'Long'),
            ('interval_bytes_recv', 'Long'),
            ('bytes_recv', 'Integer'),
            ('connect_time', 'String'),
            ('first_byte_recv_time', 'Double'),
            ('server_protocol', 'String'),
            ('video_fps', 'Integer'),
            ('video_bps', 'Integer'),
            ('audio_fps', 'Integer'),
            ('audio_bps', 'Integer'),
            ('country', 'String'),
            ('province', 'String'),
            ('isp_name', 'String'),
            ('request_method', 'String'),
            ('http_referer', 'String'),
            ('first_gop_sent_time_ms', 'Integer'),
            ('user_agent', 'String'),
            ('avg_gop_size_ms', 'String'),
            ('video_max_gap_ms', 'String'),
            ('video_dropped_ratio', 'Double'),
            ('audio_dropped_ratio', 'Double'),
            ('error_discription', 'String'),
            ('upstream_node', 'String'),
            ('discontinuous_count', 'Integer'),
            ('discontinuous_time', 'Integer'),
            ('stream_status', 'Integer')
        ]

        # 根据README.md中的字段说明定制生成器
        self.field_generators = {
            'request_id': self.generate_request_id,
            'log_time': self.generate_log_time,
            'msec': self.generate_msec,
            'request_time': self.generate_request_time,
            'stream_protocol': self.generate_stream_protocol,
            'duration': self.generate_duration,
            'total_duration': self.generate_total_duration,
            'client_addr': self.generate_client_addr,
            'server_addr': self.generate_server_addr,
            'scheme': self.generate_scheme,
            'http_method': self.generate_http_method,
            'domain': self.generate_domain,
            'request_uri': self.generate_request_uri,
            'rewrite_uri': self.generate_rewrite_uri,
            'status': self.generate_status,
            'bytes_sent': self.generate_bytes_sent,
            'interval_bytes_sent': self.generate_interval_bytes_sent,
            'body_bytes_sent': self.generate_body_bytes_sent,
            'interval_bytes_recv': self.generate_interval_bytes_recv,
            'bytes_recv': self.generate_bytes_recv,
            'connect_time': self.generate_connect_time,
            'first_byte_recv_time': self.generate_first_byte_recv_time,
            'server_protocol': self.generate_server_protocol,
            'video_fps': self.generate_video_fps,
            'video_bps': self.generate_video_bps,
            'audio_fps': self.generate_audio_fps,
            'audio_bps': self.generate_audio_bps,
            'country': self.generate_country,
            'province': self.generate_province,
            'isp_name': self.generate_isp,
            'request_method': self.generate_request_method,
            'http_referer': self.generate_http_referer,
            'first_gop_sent_time_ms': self.generate_first_gop_sent_time_ms,
            'user_agent': self.generate_user_agent,
            'avg_gop_size_ms': self.generate_avg_gop_size_ms,
            'video_max_gap_ms': self.generate_video_max_gap_ms,
            'video_dropped_ratio': self.generate_video_dropped_ratio,
            'audio_dropped_ratio': self.generate_audio_dropped_ratio,
            'error_discription': self.generate_error_description,
            'upstream_node': self.generate_upstream_node,
            'discontinuous_count': self.generate_discontinuous_count,
            'discontinuous_time': self.generate_discontinuous_time,
            'stream_status': self.generate_stream_status
        }

        # 通用类型生成器，作为后备
        self.type_generators = {
            'String': self.generate_string,
            'Integer': self.generate_integer,
            'int': self.generate_integer,
            'Long': self.generate_long,
            'long': self.generate_long,
            'Double': self.generate_double,
            'double': self.generate_double,
            'Boolean': self.generate_boolean,
            'boolean': self.generate_boolean,
            'Date': self.generate_date,
            'LocalDate': self.generate_date,
            'LocalDateTime': self.generate_datetime,
            'Float': self.generate_float,
            'float': self.generate_float
        }

    # 基本类型生成器
    def generate_string(self):
        return self.faker.word()

    def generate_integer(self):
        return random.randint(1, 1000)

    def generate_long(self):
        return random.randint(1000, 1000000)

    def generate_double(self):
        return round(random.uniform(1.0, 1000.0), 2)

    def generate_float(self):
        return round(random.uniform(0.1, 10.0), 3)

    def generate_boolean(self):
        return random.choice(['true', 'false'])

    def generate_date(self):
        return self.faker.date_this_year().strftime('%Y-%m-%d')

    def generate_datetime(self):
        return self.faker.date_time_this_year().strftime('%Y-%m-%d %H:%M:%S')

    def generate_iso8601_datetime(self):
        dt = self.faker.date_time_this_year()
        return dt.strftime('%Y-%m-%dT%H:%M:%S+08:00')

    # 特定字段生成器
    def generate_request_id(self):
        return str(uuid.uuid4())

    def generate_log_time(self):
        return self.generate_iso8601_datetime()

    def generate_msec(self):
        return int(datetime.now().timestamp() * 1000)

    def generate_request_time(self):
        return round(random.uniform(0.001, 5.0), 3)

    def generate_stream_protocol(self):
        return random.choice(['rtmp', 'http_flv', 'http_hls'])

    def generate_duration(self):
        return random.randint(10, 120)

    def generate_total_duration(self):
        return random.randint(1000, 50000)

    def generate_ip_address(self):
        return str(ipaddress.IPv4Address(random.randint(0, 2**32-1)))

    def generate_client_addr(self):
        return f"{self.generate_ip_address()}:{random.randint(1024, 65535)}"

    def generate_server_addr(self):
        return f"{self.generate_ip_address()}:80"

    def generate_scheme(self):
        return random.choice(['http', 'https', 'rtmp', 'rtmps', 'hdl', 'hls'])

    def generate_http_method(self):
        return random.choice(['GET', 'POST', 'PUT', 'DELETE', ''])

    def generate_domain(self):
        return f"play.{self.faker.domain_name()}"

    def generate_request_uri(self):
        return f"/{self.faker.uri_path(deep=1)}?myt={random.randint(1, 100)}"

    def generate_rewrite_uri(self):
        return f"/app/test.flv"

    def generate_status(self):
        return random.choice([200, 403, 404, 500])

    def generate_bytes_sent(self):
        return random.randint(10000, 10000000)

    def generate_interval_bytes_sent(self):
        return random.randint(1000, 1000000)

    def generate_body_bytes_sent(self):
        return random.randint(1000, 900000)

    def generate_interval_bytes_recv(self):
        return random.randint(100, 50000)

    def generate_bytes_recv(self):
        return random.randint(1000, 500000)

    def generate_connect_time(self):
        return self.generate_iso8601_datetime()

    def generate_first_byte_recv_time(self):
        return round(random.uniform(0.001, 2.0), 3)

    def generate_server_protocol(self):
        return random.choice(['HTTP/1.0', 'HTTP/1.1', 'HTTP/2.0', 'HTTP/3.0'])

    def generate_video_fps(self):
        return random.randint(24, 60)

    def generate_video_bps(self):
        return random.randint(500000, 8000000)

    def generate_audio_fps(self):
        return random.randint(44, 48)

    def generate_audio_bps(self):
        return random.randint(64000, 320000)

    def generate_country(self):
        return self.faker.country()

    def generate_province(self):
        return self.faker.state()

    def generate_isp(self):
        return random.choice(['China Mobile', 'China Telecom', 'China Unicom', 'China Netcom'])

    def generate_request_method(self):
        return random.choice(['forward', 'ingest', 'publish', 'play'])

    def generate_http_referer(self):
        return self.faker.uri()

    def generate_interval_bytes_sent_ms(self):
        return random.randint(100, 10000)

    def generate_user_agent(self):
        return self.faker.user_agent()

    def generate_first_gop_sent_time_ms(self):
        return random.randint(50, 500)

    def generate_avg_gop_size_ms(self):
        return random.randint(1000, 5000)

    def generate_video_max_gap_ms(self):
        return random.randint(10, 500)

    def generate_video_dropped_ratio(self):
        return round(random.uniform(0.0, 0.1), 4)

    def generate_audio_dropped_ratio(self):
        return round(random.uniform(0.0, 0.05), 4)

    def generate_error_description(self):
        errors = ['', 'Connection timeout', 'Stream not found', 'Authentication failed', 'Internal server error']
        return random.choice(errors)

    def generate_upstream_node(self):
        if random.random() > 0.5:
            return f"L2:{self.generate_ip_address()}:1935|L3:{self.generate_ip_address()}:1935"
        return ''

    def generate_discontinuous_count(self):
        return random.randint(0, 10)

    def generate_discontinuous_time(self):
        return random.randint(0, 5000)

    def generate_stream_status(self):
        return random.choice([-1, 0, 1])

    def generate_row(self):
        row = []
        for field_name, field_type in self.fields:
            try:
                # 优先使用特定字段生成器
                if field_name in self.field_generators:
                    generator = self.field_generators[field_name]
                else:
                    # 否则使用类型生成器
                    generator = self.type_generators.get(field_type, self.generate_string)
                value = generator()
                row.append(str(value))
            except Exception as e:
                print(f"Error generating value for field {field_name}: {e}")
                # 使用默认值
                if field_type in ['Integer', 'int']:
                    row.append('0')
                elif field_type in ['Long', 'long']:
                    row.append('0')
                elif field_type in ['Double', 'double', 'Float', 'float']:
                    row.append('0.0')
                elif field_type in ['Boolean', 'boolean']:
                    row.append('false')
                else:
                    row.append('')
        return row

    def generate_csv(self, output_file, num_rows):
        """Generate CSV file with random data"""
        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file, delimiter=' ')

                # 写入数据行
                for _ in range(num_rows):
                    row = self.generate_row()
                    writer.writerow(row)

            print(f"Successfully generated {num_rows} rows in {output_file}")
        except Exception as e:
            print(f"Error generating CSV file: {e}")

def main():
    # Example usage
    output_file = input("Enter the output CSV file path (default: vendor_logs.csv): ") or "vendor_logs.csv"
    num_rows = int(input("Enter the number of rows to generate (default: 10): ") or "10")

    generator = CSVGenerator()
    print(f"Generating {num_rows} rows of CSV data based on README.md specifications...")
    generator.generate_csv(output_file, num_rows)
    print(f"CSV file generated successfully at: {output_file}")
    print("The CSV file includes headers and follows the format specified in README.md")

if __name__ == "__main__":
    main()